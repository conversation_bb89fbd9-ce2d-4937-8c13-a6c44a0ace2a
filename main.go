package main

import (
	"context"
	"flag"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"program-manager/aggregatorClient"
	"program-manager/ceb"
	"program-manager/clock"
	"program-manager/config"
	"program-manager/diurnal"
	"program-manager/io"
	"program-manager/monitoring"
	"program-manager/redis"
	"program-manager/shared"
	"program-manager/types"

	"github.com/joho/godotenv"
)

const version = "0.0.11"

// Global variables for the new modular structure
var messageBridge *shared.MessageBridgeImpl
var configManager *shared.ConfigManager
var dataProcessor *shared.DataProcessor
var validator *shared.Validator

// Helper function for graceful shutdown
func shutdown() {
	log.Println("\nShutting down...")
	if messageBridge != nil {
		messageBridge.Stop()
	}
	log.Println("Disconnected from MQTT brokers")
}

// Start cloud connector functionality in background
func startCloudConnector() {
	log.SetFlags(0)

	log.Print("Starting Argus Cloud Connector... v", version)

	_ = godotenv.Load()

	// Initialize configuration manager
	configManager = shared.NewConfigManager()

	// Load configuration values
	configManager.LoadConfigValues()

	if shared.GetLogTimestamp() {
		log.SetFlags(log.LstdFlags)
	}

	// Load configurations
	if err := configManager.LoadConfigs(); err != nil {
		log.Fatalf("Error loading MQTT configs: %v", err)
	}

	configManager.PrintConfigs()

	// Initialize shared services
	dataProcessor = shared.NewDataProcessor()
	validator = shared.NewValidator()

	// Create IO module
	ioModule := io.NewIOModule(dataProcessor)
	if err := ioModule.Initialize(shared.ClientConfig); err != nil {
		log.Fatalf("Failed to initialize IO module: %v", err)
	}

	// Create Aggregator Client module
	aggregatorModule := aggregatorClient.NewAggregatorClient(dataProcessor, validator, configManager)
	if err := aggregatorModule.Initialize(shared.ServerConfig); err != nil {
		log.Fatalf("Failed to initialize Aggregator Client module: %v", err)
	}

	// Create and configure message bridge
	messageBridge = shared.NewMessageBridge()
	if err := messageBridge.ConnectModules(ioModule, aggregatorModule); err != nil {
		log.Fatalf("Failed to connect modules: %v", err)
	}

	// Start the message bridge (this starts both modules)
	if err := messageBridge.Start(); err != nil {
		log.Fatalf("Failed to start message bridge: %v", err)
	}

	// !NOTE: Wait for connections to stabilize
	time.Sleep(1 * time.Second)

	log.Printf("✓ Argus Cloud Connector is running with modular architecture")
	log.Printf("  - IO Module: Handles firmware communication")
	log.Printf("  - Aggregator Client: Handles cloud communication")
	log.Printf("  - Message Bridge: Facilitates data flow between modules")

	// Keep the cloud connector running in background
	select {}
}

func main() {
	// Parse command line flags
	var cebVerbose = flag.Bool("ceb-verbose", false, "Enable verbose logging for CEB system (shows detailed logs for 9 outputs)")
	var disableProgramManager = flag.Bool("disable-program-manager", false, "Disable Program Manager (diurnal, clock, CEB)")
	flag.Parse()

	// Always start cloud connector by default
	go startCloudConnector()

	// Skip program manager if disabled
	if *disableProgramManager {
		log.Println("Program Manager disabled - running Cloud Connector only")
		// Set up graceful shutdown for cloud connector only
		c := make(chan os.Signal, 1)
		signal.Notify(c, os.Interrupt, syscall.SIGTERM)
		log.Println("Press Ctrl+C to exit...")
		<-c
		shutdown()
		log.Println("Cloud Connector terminated gracefully")
		return
	}

	// Initialize Redis client
	redis.Initialize()
	ctx := context.Background()

	// Set up logging
	log.SetFlags(0) // No flags for simplicity
	log.Println("Program started")

	// Load program manager configuration from file
	log.Println("Loading program configuration from config/programManager.json...")
	programManagerConfig, err := config.LoadProgramManager("config/programManager.json")
	if err != nil {
		log.Fatalf("Failed to load program manager configuration: %v", err)
	}

	// Convert loaded configuration to program manager and statuses
	programManager := make(map[string]interface{})
	var statuses []types.ProgramStatus

	// Process each program from configuration
	for programName, configValue := range programManagerConfig {
		if configMap, ok := configValue.(map[string]interface{}); ok {
			if enabled, exists := configMap["enabled"]; exists {
				if enabledBool, ok := enabled.(bool); ok && enabledBool {
					// Program is enabled, set appropriate type
					switch programName {
					case "diurnalSetpoint":
						programManager[programName] = types.DiurnalSetpointConfig{Instances: []types.DiurnalInstance{}}
					case "climateEnergyBalance":
						programManager[programName] = types.CEBConfig{Enabled: true}
					case "clock":
						programManager[programName] = types.ClockConfig{Enabled: true}
					default:
						programManager[programName] = map[string]bool{"enabled": true}
					}
					statuses = append(statuses, types.ProgramStatus{Name: programName, Enabled: true})
					log.Printf("✓ %s enabled", programName)
				} else {
					statuses = append(statuses, types.ProgramStatus{Name: programName, Enabled: false})
					log.Printf("✗ %s disabled", programName)
				}
			}
		}
	}

	log.Printf("Loaded configuration for %d programs", len(statuses))

	// Handle diurnalSetpoint if enabled
	if _, ok := programManager["diurnalSetpoint"].(types.DiurnalSetpointConfig); ok {
		log.Println("\nLoading Enhanced DiurnalSetpoint Configuration...")

		// Load diurnal configuration - return error if file doesn't exist
		diurnalConfig, err := config.LoadDiurnalConfig("config/diurnalConfig.json")
		if err != nil {
			log.Fatalf("Error loading diurnal configuration: %v", err)
		}

		// Validate configuration
		if err := config.ValidateLoadedConfig(diurnalConfig); err != nil {
			log.Fatalf("Invalid diurnal configuration: %v", err)
		}
		log.Println("Enhanced configuration loaded and validated successfully!")

		// Get clock state for relative time processing (if clock is enabled)
		var clockState *types.ClockState
		if _, clockEnabled := programManager["clock"].(types.ClockConfig); clockEnabled {
			// Try to load clock configuration and get current state
			if clockConfig, err := config.LoadClockConfig("config/clockConfig.json"); err == nil {
				tempClockController := clock.NewClockController(clockConfig)
				if err := tempClockController.ProcessCycle(ctx); err == nil {
					clockState = tempClockController.GetCurrentState()
					log.Printf("Diurnal: Using clock state for relative time calculations: Dawn=%s, Dusk=%s",
						clockState.Dawn, clockState.Dusk)
				} else {
					log.Printf("Diurnal: Warning - Failed to get clock state: %v", err)
				}
			} else {
				log.Printf("Diurnal: Warning - Failed to load clock config: %v", err)
			}
		}

		if clockState == nil {
			log.Println("Diurnal: Clock not available, using default dawn/dusk times for relative time calculations")
		}

		// Process diurnal instances with clock state (includes relative time conversion)
		diurnal.ProcessInstances(ctx, diurnalConfig, clockState)

		// Print overlap status information
		diurnal.PrintPeriodOverlapStatus(diurnalConfig)

		// Start monitoring
		go monitoring.MonitorRampRates(ctx, diurnalConfig, programManager)

		// Print stored setpoints
		if err := redis.PrintAllSetpoints(ctx); err != nil {
			log.Printf("Warning: Failed to print setpoints from Redis: %v", err)
		}

		// Save configuration
		if err := config.SaveDiurnalConfig("config/diurnalConfig.json", diurnalConfig); err != nil {
			log.Fatalf("Failed to save diurnal configuration: %v", err)
		}

		programManager["diurnalSetpoint"] = map[string]bool{"enabled": true}
	}

	// Handle clock if enabled
	var clockController *clock.ClockController
	if _, ok := programManager["clock"].(types.ClockConfig); ok {
		log.Println("\nLoading Clock Program Configuration...")

		// Load clock configuration - return error if file doesn't exist
		clockConfig, err := config.LoadClockConfig("config/clockConfig.json")
		if err != nil {
			log.Fatalf("Failed to load Clock configuration: %v", err)
		}

		// Create and start Clock controller
		clockController = clock.NewClockController(clockConfig)

		// Start Clock processing in a separate goroutine
		go func() {
			// Run immediately on startup
			log.Printf("Clock: Running initial cycle...")
			if err := clockController.ProcessCycle(ctx); err != nil {
				log.Printf("Clock: Error in initial processing cycle: %v", err)
			}

			ticker := time.NewTicker(time.Duration(clockConfig.UpdateInterval) * time.Second)
			defer ticker.Stop()

			log.Printf("Clock: Scheduled to update every %d seconds", clockConfig.UpdateInterval)

			for {
				select {
				case <-ticker.C:
					log.Printf("Clock: Running scheduled update cycle...")
					if err := clockController.ProcessCycle(ctx); err != nil {
						log.Printf("Clock: Error in processing cycle: %v", err)
					}
				case <-ctx.Done():
					log.Printf("Clock: Shutting down...")
					return
				}
			}
		}()

		log.Println("Clock Program started successfully!")

		// Only store enabled status in programManager.json
		programManager["clock"] = map[string]bool{"enabled": true}
	}

	// Handle climateEnergyBalance if enabled
	var cebController *ceb.CEBController
	if _, ok := programManager["climateEnergyBalance"].(types.CEBConfig); ok {
		log.Println("\nLoading Enhanced Climate Energy Balance Configuration...")

		// Load CEB configuration - return error if file doesn't exist
		cebConfig, err := config.LoadCEBConfig("config/cebConfig.json")
		if err != nil {
			log.Fatalf("Failed to load CEB configuration: %v", err)
		}

		// Create and start CEB controller
		cebController = ceb.NewCEBController(cebConfig)

		// Set verbose logging based on command line flag
		cebController.SetVerboseLogging(*cebVerbose)

		// Start CEB processing in a separate goroutine
		go func() {
			ticker := time.NewTicker(5 * time.Second) // Run every 5 seconds
			defer ticker.Stop()

			for {
				select {
				case <-ticker.C:
					if err := cebController.ProcessCycle(ctx); err != nil {
						log.Printf("CEB: Error in processing cycle: %v", err)
					}
				case <-ctx.Done():
					return
				}
			}
		}()

		log.Println("Climate Energy Balance system started successfully!")

		// Only store enabled status in programManager.json
		programManager["climateEnergyBalance"] = map[string]bool{"enabled": true}
	}

	// Write to config/programManager.json
	if err := config.SaveProgramManager("config/programManager.json", programManager); err != nil {
		log.Fatalf("Failed to create config/programManager.json: %v", err)
	}

	log.Printf("config/programManager.json created/updated.\n\n")

	// Print enabled and disabled programs in aligned format
	maxLen := 0
	for _, s := range statuses {
		if l := len(s.Name); l > maxLen {
			maxLen = l
		}
	}
	for _, s := range statuses {
		status := "✗ disabled"
		if s.Enabled {
			status = "✓ enabled"
		}
		log.Printf("%-*s %s", maxLen, s.Name, status)
	}

	// Set up graceful shutdown for both systems
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)

	log.Println("\nPress Ctrl+C to exit...")

	// Wait for interrupt signal
	<-c

	// Always shutdown cloud connector (it always runs)
	shutdown()

	log.Println("Program terminated gracefully")
}
